// ignore_for_file: prefer_final_fields

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:promobell/src/models/categorias.dart';
import 'package:share_plus/share_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../theme/svg_icons.dart';
import '../../../models/categorias_menu.dart';
import '../../../models/product.dart';
import '../../../services/logs/app_logger.dart';
import '../../../services/supabase/categorias/delete/delete_categorias.dart';
import '../../../services/supabase/categorias/get/get_categorias.dart';
import '../../../services/supabase/categorias/put/put_categorias.dart';
import '../../../services/supabase/produtos/get/get_produtos.dart';
import '../../../services/supabase/produtos/put/put_produtos.dart';
import '../../../services/supabase/reporte_problemas/put/put_report_problem.dart';
import '../../../services/supabase/utils/get/get_utils.dart';
import '../../categories/ui/widgets/detail/custom_dialog.dart';
import '../ui/widget/offers_page/debouncer.dart';

enum TipoListaProdutos { geral, busca, filtrosPersonalizados }

class OffersController with ChangeNotifier {
  final GetProdutos _getProdutos;

  OffersController({required GetProdutos getProdutos})
    : _getProdutos = getProdutos;

  //==========================================================================
  // DEPENDÊNCIAS E INICIALIZAÇÃO
  //==========================================================================
  final List<Product> _produtos = [];
  List<Product> get produtos => _produtos;
  final List<Product> _produtosOfertasCategoria = [];
  List<Product> get produtosOfertasCategoria =>
      _produtosOfertasCategoria;

  PutProdutos putProdutosMetodos = PutProdutos();
  GetCategorias getCategoriasMetodos = GetCategorias();
  PutCategorias putCategoriasMetodos = PutCategorias();
  DeleteCategorias deleteCategoriasMetodos = DeleteCategorias();
  GetUtils getUtilsMetodos = GetUtils();
  PutReportProblem putReportProblemMetodos = PutReportProblem();
  // GetStory getStory = GetStory();
  // PutStory putStory = PutStory();
  final Debouncer _debouncer = Debouncer(milliseconds: 500);

  //==========================================================================
  // FILTROS
  //==========================================================================
  TipoListaProdutos _tipoListaAtual = TipoListaProdutos.geral;
  TipoListaProdutos get tipoListaAtual => _tipoListaAtual;

  void setTipoLista(TipoListaProdutos tipo) {
    _tipoListaAtual = tipo;
    // Usar addPostFrameCallback para evitar notifyListeners durante build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  List<Product> aplicarOrdenacaoRapida(
    List<Product> lista,
    String filtro,
  ) {
    final novaLista = List<Product>.from(lista);

    if (filtro == 'Recentes') {
      novaLista.sort((a, b) => b.id.compareTo(a.id));
    } else if (filtro == 'Com frete grátis') {
      novaLista.sort((a, b) {
        if (a.frete != b.frete) return b.frete ? 1 : -1;
        return b.id.compareTo(a.id);
      });
    } else if (filtro == 'Tem cupom') {
      novaLista.sort((a, b) {
        if ((a.cupom.isNotEmpty) != (b.cupom.isNotEmpty)) {
          return b.cupom.isNotEmpty ? 1 : -1;
        }
        return b.id.compareTo(a.id);
      });
    }

    return novaLista;
  }

  //==========================================================================
  // ATUALIZAÇÃO DO APP
  //==========================================================================
  bool precisaAtualizar = true;

  Future<(bool precisaAtualizar, bool ehForcada)>
  verificarAtualizacaoDoApp() async {
    var utils = await getUtilsMetodos.getProduto();
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    double versionApp = formatToDoubleVersion(packageInfo.version);
    double version = formatToDoubleVersion(utils.version);
    double criticalVersion = formatToDoubleVersion(
      utils.criticalVersion,
    );

    bool precisaAtualizar = versionApp < version;
    bool ehForcada = versionApp < criticalVersion;

    return (precisaAtualizar, ehForcada);
  }

  void setValuePrecisaAtualizar(bool value) {
    precisaAtualizar = value;
    notifyListeners();
  }

  double formatToDoubleVersion(String value) {
    List<String> parts = value.split('.');

    int major = parts.isNotEmpty ? int.tryParse(parts[0]) ?? 0 : 0;
    int minor = parts.length > 1 ? int.tryParse(parts[1]) ?? 0 : 0;
    int patch = parts.length > 2 ? int.tryParse(parts[2]) ?? 0 : 0;

    // Exemplo: 1.2.3 -> 1 + 2/100 + 3/10000 = 1.0203
    return major + (minor / 100) + (patch / 10000);
  }

  //==========================================================================
  // GESTÃO DE CATEGORIAS
  //==========================================================================
  //List<CategoriaMenu> get categorias => CategoriaMenu.categorias;

  static final List<CategoriaMenu> _categoriasRandomizadas =
      List.from(CategoriaMenu.categorias)..shuffle(Random());
  static List<CategoriaMenu> get categorias =>
      _categoriasRandomizadas;
  int get tamanhoCategorias => categorias.length;

  CategoriaMenu getCategory(String categoryName) {
    return CategoriaMenu.categorias.firstWhere(
      (cat) => cat.nome == categoryName,
      orElse: () => CategoriaMenu.categorias.first,
    );
  }

  final Map<int, bool> _followedCategories =
      {}; // Se a categoria foi seguida
  final Map<int, int> _followersCount =
      {}; // Quantidade de seguidores por categoria

  bool isCategoryFollowed(int categoryId) =>
      _followedCategories[categoryId] ?? false;
  int getFollowersCount(int categoryId) =>
      _followersCount[categoryId] ?? 0;

  Future<void> fetchFollowersCount(int categoryId) async {
    final count = await fetchCategoryFollowers(categoryId);
    _followersCount[categoryId] = int.parse(count);
    notifyListeners();
  }

  Future<void> fetchFollowStatus(
    int categoryId,
    String userEmail,
  ) async {
    final isFollowing = await isFollowingCategory(
      categoryId: categoryId,
      userEmail: userEmail,
    );
    _followedCategories[categoryId] = isFollowing;
    notifyListeners();
  }

  Future<String> fetchCategoryFollowers(int categoryId) async {
    try {
      int followerCount = await getCategoriasMetodos
          .getCategoryFollowersCount(categoryId);
      return followerCount.toString();
    } catch (e) {
      return '0';
    }
  }

  Future<bool> isFollowingCategory({
    required int categoryId,
    required String userEmail,
  }) {
    try {
      return getCategoriasMetodos.isFollowingCategory(
        categoryId,
        userEmail,
      );
    } catch (e) {
      AppLogger.logError(
        'Erro ao verificar se o usuário está seguindo a categoria',
        e,
        StackTrace.current,
      );
      return Future.value(false);
    }
  }

  Future<void> handleFollowCategory({
    required int categoryId,
    required String userEmail,
    required String categoryName,
  }) async {
    try {
      await putCategoriasMetodos.followCategory(
        categoryId,
        userEmail,
        categoryName,
      );
    } catch (e) {
      AppLogger.logError(
        'Erro ao seguir a categoria',
        e,
        StackTrace.current,
      );
    }
  }

  Future<void> unfollowCategory({
    required int categoryId,
    required String userEmail,
    required String categoryName,
  }) async {
    try {
      await deleteCategoriasMetodos.unFollowCategory(
        categoryId,
        userEmail,
        categoryName,
      );
    } catch (e) {
      AppLogger.logError(
        'Erro ao deixar de seguir a categoria',
        e,
        StackTrace.current,
      );
    }
  }

  Future<void> toggleFollowCategory(
    int categoryId,
    String userEmail,
    String categoryName,
  ) async {
    final isFollowing = _followedCategories[categoryId] ?? false;

    _followedCategories[categoryId] = !isFollowing;
    _followersCount[categoryId] =
        isFollowing
            ? (_followersCount[categoryId] ?? 0) - 1
            : (_followersCount[categoryId] ?? 0) + 1;

    notifyListeners();

    try {
      if (isFollowing) {
        await unfollowCategory(
          categoryId: categoryId,
          userEmail: userEmail,
          categoryName: categoryName,
        );
      } else {
        await handleFollowCategory(
          categoryId: categoryId,
          userEmail: userEmail,
          categoryName: categoryName,
        );
      }
    } catch (e) {
      AppLogger.logError(
        'Erro ao alternar o seguimento da categoria',
        e,
        StackTrace.current,
      );
    }
  }

  List<dynamic> generateItens() {
    final List<dynamic> items = [];

    switch (_tipoListaAtual) {
      case TipoListaProdutos.geral:
        final int produtosPorGrupo = 6;
        int categoriaIndex = 0;

        if (categorias.isNotEmpty) {
          items.add(categorias[categoriaIndex]);
          categoriaIndex++;
        }

        for (int i = 0; i < _produtos.length; i += produtosPorGrupo) {
          items.addAll(_produtos.skip(i).take(produtosPorGrupo));

          if (categoriaIndex < categorias.length) {
            items.add(categorias[categoriaIndex]);
            categoriaIndex++;
          }
        }
        break;
      case TipoListaProdutos.busca:
        items.addAll(_produtosFiltrados);
        break;
      case TipoListaProdutos.filtrosPersonalizados:
        items.addAll(_produtosFiltradosByUser);
        break;
    }

    return items;
  }

  final Map<String, bool> _showVideoState = {};
  // Função para obter o estado de showVideo
  bool getShowVideo(String categoriaNome) {
    if (!_showVideoState.containsKey(categoriaNome)) {
      _showVideoState[categoriaNome] =
          Random().nextBool(); // Gera o valor apenas uma vez
    }
    return _showVideoState[categoriaNome]!;
  }

  //==========================================================================
  // GESTÃO DE PRODUTOS
  //==========================================================================

  final Map<String, DateTime> reports = {};
  final List<DateTime> userReportTimestamps = [];
  static const int reportCooldownMinutes = 2;
  static const int maxReportsBeforeBlock = 3;
  static const int blockDurationHours = 72;

  bool podeReportar(String idProduto) {
    final agora = DateTime.now();

    if (userReportTimestamps.length >= maxReportsBeforeBlock) {
      final primeiroReport = userReportTimestamps.first;
      if (agora.difference(primeiroReport).inHours <
          blockDurationHours) {
        return false;
      } else {
        userReportTimestamps.clear();
      }
    }

    return true;
  }

  bool jaReportouProduto(String idProduto) {
    if (reports.containsKey(idProduto)) {
      final ultimaVez = reports[idProduto]!;
      return DateTime.now().difference(ultimaVez).inMinutes <
          reportCooldownMinutes;
    }
    return false;
  }

  void registrarReport(String idProduto) {
    final agora = DateTime.now();
    reports[idProduto] = agora;
    userReportTimestamps.add(agora);
  }

  List<Product> _produtosFiltrados = [];
  List<Product> get produtosFiltrados =>
      _produtosFiltrados.isEmpty ? _produtos : _produtosFiltrados;

  List<Product> produtosPorCategoria = [];
  int _offsetCategoria = 0;
  final int _limitCategoria = 20;
  bool _isLoadingMoreCategoria = false;
  bool _hasMorCategoriae = true;
  bool get hasMorCategoriae => _hasMorCategoriae;

  bool get isLoadingMoreCategoria => _isLoadingMoreCategoria;

  void resetPaginationCategoria() {
    _offsetCategoria = 0;
    _hasMorCategoriae = true;
  }

  Future<void> getProdutosPorCategoria(
    String categoriaNome, {
    bool isLoadMore = false,
  }) async {
    if (_isLoadingMoreCategoria || !_hasMorCategoriae) return;

    try {
      _isLoadingMoreCategoria = true;
      setLoadingFilterCategory(true);

      final produtos = await _getProdutos.getProdutosPorCategoria(
        categoriaNome,
        limit: _limitCategoria,
        offset: _offsetCategoria,
      );

      if (produtos.isEmpty || produtos.length < _limitCategoria) {
        _hasMorCategoriae = false;
      }

      _offsetCategoria += _limitCategoria;

      if (isLoadMore) {
        produtosPorCategoria.addAll(produtos);
      } else {
        produtosPorCategoria = produtos;
      }

      produtosPorCategoria.sort(
        (a, b) => b.criadoEm.compareTo(a.criadoEm),
      );

      addProduto(produtosPorCategoria);
      filterProdutosPorCategoria(categoriaNome);
      notifyListeners();
    } catch (e) {
      AppLogger.logError(
        'Erro ao obter produtos por categoria',
        e,
        StackTrace.current,
      );
    } finally {
      _isLoadingMoreCategoria = false;
      setLoadingFilterCategory(false);
    }
  }

  String selectedFilter = 'Recentes';
  String selectedFilterCategorias = 'Recentes';
  String searchQuery = '';

  void addProduto(List<Product> produtos) {
    _produtos.clear();
    _produtosOfertasCategoria.clear();
    produtos.sort((a, b) => b.criadoEm.compareTo(a.criadoEm));
    produtosOfertasCategoria.sort(
      (a, b) => b.criadoEm.compareTo(a.criadoEm),
    );
    selectedFilter = 'Recentes';
    _produtos.addAll(produtos);
    _produtosOfertasCategoria.addAll(produtos);
    notifyListeners();
  }

  bool isLoading = false;

  void setLoadingValue(bool value) {
    isLoading = value;
    if (hasListeners) {
      notifyListeners();
    }
  }

  int _currentOffset = 0;
  final int _limit = 100;
  bool _hasMore = true;
  bool get hasMore => _hasMore;

  bool isLoadingInitial = true;
  bool isFetchingMore = false;

  Future<void> fetchNextProdutos() async {
    if (isFetchingMore || !_hasMore) return;

    isFetchingMore = true;
    notifyListeners();

    final List<Product> newProdutos = await _getProdutos
        .getAllProdutos(limit: _limit, offset: _currentOffset);

    if (newProdutos.length < _limit) {
      _hasMore = false;
    }

    _currentOffset += newProdutos.length;
    _produtos.addAll(newProdutos);
    _produtosOfertasCategoria.addAll(newProdutos);
    notifyListeners();

    isFetchingMore = false;
  }

  bool isProdutosDaMesmaCategoria(List<Product> produtos) {
    final idCategoria = produtos.first.idCategoria;
    for (var produto in produtos) {
      if (produto.idCategoria != idCategoria) {
        return true;
      }
    }
    return false;
  }

  Future<void> refreshProdutos() async {
    if (_produtos.isNotEmpty &&
        _produtos.isNotEmpty &&
        _produtos.length == _produtos.length &&
        isProdutosDaMesmaCategoria(_produtos)) {
      return;
    }

    _currentOffset = 0;
    _hasMore = true;
    isLoadingInitial = true;
    // Usar addPostFrameCallback para evitar notifyListeners durante build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });

    _produtos.clear();
    _produtosOfertasCategoria.clear();
    await fetchNextProdutos();

    isLoadingInitial = false;
    // Usar addPostFrameCallback para evitar notifyListeners durante build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  bool _isSearching = false;
  bool get isSearching => _isSearching;

  void setIsSearching(bool value) {
    _isSearching = value;
    // Usar addPostFrameCallback para evitar notifyListeners durante build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  Future<void> buscarProdutos(String query) async {
    searchQuery = query.trim().toLowerCase();

    setIsSearching(true);
    await Future.delayed(const Duration(seconds: 1));

    try {
      if (searchQuery.isEmpty) {
        _produtosFiltrados = List.from(_produtos);
        setTipoLista(TipoListaProdutos.geral);
      } else {
        _produtosFiltrados = await _getProdutos.searchProdutos(
          searchQuery,
        );
        setTipoLista(TipoListaProdutos.busca);
      }

      setFilter(selectedFilter);
    } catch (e) {
      AppLogger.logError(
        'Erro ao buscarProdutos',
        e,
        StackTrace.current,
      );
      _produtosFiltrados = [];
      setTipoLista(TipoListaProdutos.busca);
    }

    setIsSearching(false);
  }

  List<Product> getProdutosFiltradosCategorias(String query) {
    return produtos
        .where(
          (produto) => produto.categoria.toLowerCase().contains(
            query.toLowerCase(),
          ),
        )
        .toList();
  }

  filterProdutosPorCategoria(String query) {
    if (produtosPorCategoria.isEmpty) {
      // se a lista de produtos por categoria estiver vazia, então filtra a lista de produtos gerais
      produtosPorCategoria =
          produtos
              .where(
                (produto) => produto.categoria.toLowerCase().contains(
                  query.toLowerCase(),
                ),
              )
              .toList();
    } else {
      // verifica se a lista de produtos por categoria contem produtos somente da categoria selecionada
      if (produtosPorCategoria.any(
        (produto) =>
            produto.categoria.toLowerCase() == query.toLowerCase(),
      )) {
        // se a lista de produtos por categoria contem produtos da categoria selecionada, e esta tudo ok, então não faz nada
      } else {
        // se a lista de produtos por categoria não contem produtos da categoria selecionada, então filtra a lista de produtos gerais
        produtosPorCategoria =
            produtos
                .where(
                  (produto) => produto.categoria
                      .toLowerCase()
                      .contains(query.toLowerCase()),
                )
                .toList();
      }
    }
  }

  List<Product> getProdutosCategorias(String query) {
    return produtosOfertasCategoria
        .where(
          (produto) => produto.categoria.toLowerCase().contains(
            query.toLowerCase(),
          ),
        )
        .toList();
  }

  Future<List<Product>> getProdutosFiltradosCategoriasInSupabse({
    required String categoria,
  }) async {
    produtosPorCategoria.clear();
    produtosPorCategoria = await _getProdutos
        .getProdutosPorCategoriaAleatorios(categoria);
    return produtos;
  }

  void setFilter(String filtro) {
    selectedFilter = filtro;
    setLoadingFilter(true);

    List<Product> listaParaOrdenar;

    // Usa a lista com base no tipo atual, não só no conteúdo
    switch (_tipoListaAtual) {
      case TipoListaProdutos.filtrosPersonalizados:
        listaParaOrdenar = _produtosFiltradosByUser;
        break;
      case TipoListaProdutos.busca:
        listaParaOrdenar = _produtosFiltrados;
        break;
      case TipoListaProdutos.geral:
        listaParaOrdenar = _produtos;
        break;
    }

    final ordenados = aplicarOrdenacaoRapida(
      listaParaOrdenar,
      filtro,
    );

    // Atualiza a lista correta
    switch (_tipoListaAtual) {
      case TipoListaProdutos.filtrosPersonalizados:
        _produtosFiltradosByUser = ordenados;
        break;
      case TipoListaProdutos.busca:
        _produtosFiltrados = ordenados;
        break;
      case TipoListaProdutos.geral:
        _produtos
          ..clear()
          ..addAll(ordenados);
        break;
    }

    notifyListeners();
    setLoadingFilter(false);
  }

  void setFilterCategory(String filter) {
    selectedFilterCategorias = filter;
    setLoadingFilterCategory(true);

    if (_produtosFiltradosByUser.isNotEmpty) {
      _produtosFiltradosByUser = aplicarOrdenacaoRapida(
        _produtosFiltradosByUser,
        filter,
      );
    } else {
      produtosPorCategoria = aplicarOrdenacaoRapida(
        produtosPorCategoria,
        filter,
      );
    }

    notifyListeners();
    setLoadingFilterCategory(false);
  }

  bool loadingFilter = false;
  void setLoadingFilter(bool value) {
    if (value) {
      loadingFilter = true;
      notifyListeners();
      Future.delayed(const Duration(milliseconds: 150), () {
        loadingFilter = false;
        notifyListeners();
      });
    } else {
      loadingFilter = false;
      notifyListeners();
    }
  }

  bool loadingFilterCategory = false;
  void setLoadingFilterCategory(bool value) {
    if (value) {
      loadingFilterCategory = true;

      notifyListeners();
      Future.delayed(const Duration(milliseconds: 150), () {
        loadingFilterCategory = false;

        notifyListeners();
      });
    } else {
      loadingFilterCategory = false;

      notifyListeners();
    }
  }

  Future<bool> reportProblem(String reason, id) async {
    try {
      await putReportProblemMetodos.insertProblem(id, reason);
      return true;
    } catch (e) {
      AppLogger.logError(
        'Erro ao reportar problema',
        e,
        StackTrace.current,
      );
      return false;
    }
  }

  Animation<double> animatedImage(Animation<double> animation) {
    return Tween<double>(begin: 0.0, end: 1.0)
        .chain(
          CurveTween(
            curve: const Interval(0.3, 1.0, curve: Curves.slowMiddle),
          ),
        )
        .animate(animation);
  }

  Future<int> getTotalPorCategoria(String categoriaNome) async {
    return await _getProdutos.getTotalProdutosPorCategoria(
      categoriaNome,
    );
  }

  Future<void> registrarVisualizacaoProduto({
    required int produtoId,
  }) async {
    try {
      final supabase = Supabase.instance.client;
      final user = supabase.auth.currentUser;
      if (user == null || user.email == null) return;
      await supabase.from('visualizacoes_produtos').insert({
        'produto_id': produtoId,
        'email_usuario': user.email,
      });

      AppLogger.logInfo(
        '✅ Visualização registrada para o produto $produtoId',
      );
    } catch (e) {
      AppLogger.logError(
        '❌ Erro ao registrar visualização do produto',
        e,
        StackTrace.current,
      );
    }
  }

  Future<List<Product>> carregarTopProdutos({
    required String categoria,
  }) async {
    final produtos = await _getProdutos.buscarTopProdutosDetalhados(
      dias: 15,
      categoria: categoria,
    );
    notifyListeners();
    return produtos;
  }

  //==========================================================================
  // GESTÃO DE LIKES
  //==========================================================================
  final Map<int, bool> _likedProducts = {};
  final Map<int, int> _likesCount = {};

  bool isProductLiked(int productId) =>
      _likedProducts[productId] ?? false;
  int getLikesCount(int productId) => _likesCount[productId] ?? 0;

  Future<void> fetchLikesCount(int productId) async {
    final likes = await fetchLikes(productId: productId);
    _likesCount[productId] = likes;
    notifyListeners();
  }

  Future<int> fetchLikes({required int productId}) async {
    try {
      return await _getProdutos.getLikeCount(productId);
    } catch (e) {
      AppLogger.logError(
        'Erro ao obter o número de curtidas',
        e,
        StackTrace.current,
      );
      return 0;
    }
  }

  Future<void> fetchLikeStatus(
    int productId,
    String userEmail,
  ) async {
    final isLiked = await hasLikedProduct(
      productId: productId,
      userEmail: userEmail,
    );
    _likedProducts[productId] = isLiked;
    notifyListeners();
  }

  Future<bool> hasLikedProduct({
    required int productId,
    required String userEmail,
  }) {
    try {
      return _getProdutos.hasLikedProduct(productId, userEmail);
    } catch (e) {
      AppLogger.logError(
        'Erro ao verificar se o usuário curtiu o produto',
        e,
        StackTrace.current,
      );
      return Future.value(false);
    }
  }

  Future<void> toggleLike(int productId, String userEmail) async {
    final isLiked = _likedProducts[productId] ?? false;
    if (isLiked) {
      _debouncer.run(() async => await unlikeProduct(productId));
      _likesCount[productId] = (_likesCount[productId] ?? 0) - 1;
    } else {
      _debouncer.run(
        () async => await likeProduct(productId, userEmail),
      );
      _likesCount[productId] = (_likesCount[productId] ?? 0) + 1;
    }
    _likedProducts[productId] = !isLiked;
    notifyListeners();
  }

  Future<void> likeProduct(int productId, String email) async {
    try {
      await putProdutosMetodos.likeProduct(productId, email);
    } catch (e) {
      AppLogger.logError(
        'Erro ao marcar que gostou do produto',
        e,
        StackTrace.current,
      );
    }
  }

  Future<void> unlikeProduct(int productId) async {
    try {
      await putProdutosMetodos.unlikeProduct(productId);
    } catch (e) {
      AppLogger.logError(
        'Erro ao ao desmarcar que gostou do produto',
        e,
        StackTrace.current,
      );
    }
  }

  String numberLikes(String likes) {
    int likesInt = int.parse(likes);
    if (likesInt < 1000) {
      return likes;
    } else if (likesInt < 1000000) {
      return '${(likesInt / 1000).toStringAsFixed(1)}K';
    } else if (likesInt < 1000000000) {
      return '${(likesInt / 1000000).toStringAsFixed(1)}M';
    } else {
      return '${(likesInt / 1000000000).toStringAsFixed(1)}B';
    }
  }

  //==========================================================================
  // DEEP LINK & URLS
  //==========================================================================

  bool _isLoadingShare = false;
  bool get isLoadingShare => _isLoadingShare;

  Future<String> generateShareableLink(
    String path,
    Map<String, String> params,
  ) async {
    final queryString = Uri(queryParameters: params).query;
    return 'https://promobell.com.br/$path?$queryString'.replaceAll(
      '\$path',
      path,
    );
  }

  void launchWhatsApp(Product product) async {
    _isLoadingShare = true;
    notifyListeners();

    try {
      final link = await generateShareableLink('product', {
        'id': product.id.toString(),
      });

      String precoFormatado = convertDoubleToString(
        product.precoAtual,
      );
      if (!precoFormatado.contains('R\$')) {
        precoFormatado = 'R\$ $precoFormatado';
      }

      String text =
          'Ei! Olha só essa oferta que eu encontrei no app Promobell, é a sua cara:\n\n'
          '${product.titulo}\n\n'
          '💰 $precoFormatado\n\n'
          '🔗 $link';

      String imageUrl = product.urlImagem;
      if (imageUrl.isNotEmpty) {
        SharePlus.instance.share(ShareParams(text: text));
      } else {
        SharePlus.instance.share(ShareParams(text: text));
      }
    } catch (e) {
      try {
        final link = await generateShareableLink('product', {
          'id': product.id.toString(),
        });

        String precoFormatado = convertDoubleToString(
          product.precoAtual,
        );
        if (!precoFormatado.contains('R\$')) {
          precoFormatado = 'R\$ $precoFormatado';
        }

        String text =
            'Ei! Olha só essa oferta que eu encontrei no app Promobell, é a sua cara:\n\n'
            '${product.titulo}\n\n'
            '💰 $precoFormatado\n\n'
            '🔗 $link';

        SharePlus.instance.share(ShareParams(text: text));
      } catch (fallbackError) {
        rethrow;
      }
    } finally {
      _isLoadingShare = false;
      notifyListeners();
    }
  }

  void launchProductUrl(String productUrl) async {
    final Uri url = Uri.parse(productUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  void shareCategory(
    CategoriaMenu categoria, {
    required bool isFollowing,
  }) async {
    _isLoadingShare = true;
    notifyListeners();

    try {
      final link = await generateShareableLink('category', {
        'id': categoria.id.toString(),
      });

      String text =
          isFollowing
              ? 'Ei! Olha só essa categoria chamada ${categoria.nome} que eu encontrei no app Promobell, é a sua cara!\n\n$link'
              : 'Ei! Olha só essa categoria chamada ${categoria.nome} que eu encontrei no app Promobell, é a sua cara!\n\n$link';

      String imageUrl = categoria.fotoPequena;
      if (!imageUrl.startsWith('http')) {
        imageUrl =
            'https://promobell.com.br/assets/categorias/${categoria.nome.toLowerCase().replaceAll(' ', '_')}/category-image-${categoria.nome.toLowerCase().replaceAll(' ', '')}.png';
      }

      SharePlus.instance.share(ShareParams(text: text));
    } catch (e) {
      rethrow;
    } finally {
      _isLoadingShare = false;
      notifyListeners();
    }
  }

  //==========================================================================
  // FOLLOW BUTTON LOGIC
  //==========================================================================
  bool _following = false;
  bool get following => _following;

  setFollowing(bool value) {
    _following = value;
    notifyListeners();
  }

  void followButtonLogic(BuildContext context) {
    _following = !_following;
    if (!_following) {
      CustomDialog.show(
        context,
        onConfirm: () => onConfirm(context),
        onCancel: () => onCancel(context),
      );
    }
    notifyListeners();
  }

  void onConfirm(BuildContext context) {
    Navigator.of(context).pop();
    _following = false;
    notifyListeners();
  }

  void onCancel(BuildContext context) {
    Navigator.of(context).pop();
    _following = true;
    notifyListeners();
  }

  //==========================================================================
  // UTILITÁRIOS E FORMATOS
  //==========================================================================
  String convertDoubleToString(double value) {
    final formatter = NumberFormat('#,##0.00', 'pt_BR');
    return formatter.format(value);
  }

  String getTimeToNow(DateTime dateTime) {
    final now = DateTime.now().toLocal();
    final diff = now.difference(dateTime.toLocal());
    if (diff.inMinutes < 1) return 'Agora';
    if (diff.inMinutes < 60) return '${diff.inMinutes} min';
    if (diff.inHours < 24) return '${diff.inHours} h';
    if (diff.inDays < 365) return '${diff.inDays} d';
    return '${diff.inDays ~/ 365} a';
  }

  String getTimeToNowStory(DateTime dateTime) {
    // Ajusta "now" para UTC+3 também, sem mexer na data original
    final now = DateTime.now().toUtc().add(Duration(hours: 3));
    final diff = now.difference(dateTime);

    if (diff.inMinutes < 1) return 'Agora';
    if (diff.inMinutes < 60) return '${diff.inMinutes} min';
    if (diff.inHours < 24) return '${diff.inHours} h';
    if (diff.inDays < 365) return '${diff.inDays} d';
    return '${diff.inDays ~/ 365} a';
  }

  String platformName(String name) {
    const Map<String, String> platformIcons = {
      'Mercado Livre': SvgIcons.iconeMercadoLivre,
      'Amazon': SvgIcons.iconeAmazon,
      'Magalu': SvgIcons.iconeMagalu,
      'Magazine Luiza': SvgIcons.iconeMagaluPNG,
    };
    return platformIcons[name] ?? '';
  }

  // like button

  void handleLike(int productId, String userEmail) {
    final isLiked = _likedProducts[productId] ?? false;
    _likedProducts[productId] = !isLiked;
    _likesCount[productId] =
        (_likesCount[productId] ?? 0) + (isLiked ? -1 : 1);
    notifyListeners();

    _debouncer.run(() async {
      try {
        if (isLiked) {
          await putProdutosMetodos.unlikeProduct(productId);
        } else {
          await putProdutosMetodos.likeProduct(productId, userEmail);
        }
      } catch (e) {
        _likedProducts[productId] = isLiked;
        _likesCount[productId] =
            (_likesCount[productId] ?? 0) + (isLiked ? 1 : -1);
        notifyListeners();
      }
    });
  }

  Future<void> initLikeState(int productId, String userEmail) async {
    final likes = await _getProdutos.getLikeCount(productId);
    final isLiked = await _getProdutos.hasLikedProduct(
      productId,
      userEmail,
    );

    _likesCount[productId] = likes;
    _likedProducts[productId] = isLiked;
    notifyListeners();
  }

  // Save Products

  int _savedProductsPage = 0;
  bool _isLoadingMoreSaved = false;
  bool _hasMoreSavedProducts = true;

  bool get isLoadingMoreSaved => _isLoadingMoreSaved;
  bool get hasMoreSavedProducts => _hasMoreSavedProducts;

  final List<Product> _savedProducts = [];
  final List<int> _savedProductIds = [];

  List<Product> get savedProducts => _savedProducts;
  List<int> get savedProductIds => _savedProductIds;

  Future<void> initSavedProducts({int limit = 10}) async {
    _savedProductsPage = 0;
    _hasMoreSavedProducts = true;
    _savedProducts.clear();
    await loadSavedProductIds();

    if (_savedProductIds.isEmpty) {
      notifyListeners();
      return;
    }

    final produtos = await _getProdutos.getProdutosPorIdsComPaginacao(
      ids: _savedProductIds,
      limit: limit,
      offset: _savedProductsPage * limit,
    );

    _savedProducts.addAll(produtos);

    if (produtos.length < limit) _hasMoreSavedProducts = false;

    notifyListeners();
  }

  Future<void> loadMoreSavedProducts({int limit = 10}) async {
    if (_isLoadingMoreSaved || !_hasMoreSavedProducts) return;

    _isLoadingMoreSaved = true;
    notifyListeners();

    _savedProductsPage++;

    final produtos = await _getProdutos.getProdutosPorIdsComPaginacao(
      ids: _savedProductIds,
      limit: limit,
      offset: _savedProductsPage * limit,
    );

    if (produtos.isEmpty || produtos.length < limit) {
      _hasMoreSavedProducts = false;
    }

    _savedProducts.addAll(produtos);
    _isLoadingMoreSaved = false;
    notifyListeners();
  }

  Future<void> addSavedProduct(Product product) async {
    if (!_savedProductIds.contains(product.id)) {
      final userEmail =
          Supabase.instance.client.auth.currentUser?.email ?? '';
      await putProdutosMetodos.salvarProduto(product.id, userEmail);
      _savedProductIds.add(product.id);

      if (!_savedProducts.any((p) => p.id == product.id)) {
        _savedProducts.add(product);
      }

      notifyListeners();
    }
  }

  Future<void> removeSavedProduct(Product product) async {
    final userEmail =
        Supabase.instance.client.auth.currentUser?.email ?? '';
    await putProdutosMetodos.removerProdutoSalvo(
      product.id,
      userEmail,
    );
    _savedProductIds.remove(product.id);
    _savedProducts.removeWhere((p) => p.id == product.id);
    notifyListeners();
  }

  // Alterna entre salvar/remover o produto
  Future<void> toggleSavedProduct(Product product) async {
    if (_savedProductIds.contains(product.id)) {
      await removeSavedProduct(product);
    } else {
      await addSavedProduct(product);
    }
  }

  Future<void> removedAllSavedProducts(String userEmail) async {
    await putProdutosMetodos.removerTodosProdutosSalvos(userEmail);
    _savedProductIds.clear();
    _savedProducts.clear();
    notifyListeners();
  }

  Future<void> loadSavedProductIds() async {
    final userEmail =
        Supabase.instance.client.auth.currentUser?.email ?? '';
    final ids = await _getProdutos.getSavedProductIds(userEmail);
    _savedProductIds
      ..clear()
      ..addAll(ids);
  }

  // Verifica se o produto está salvo
  bool isProductSavedSync(Product product) {
    return _savedProductIds.contains(product.id);
  }

  //==========================================================================
  // Filters
  List<Categoria> _categoriasAndSubcategorias = [];
  List<Categoria> get categoriasAndSubcategorias =>
      _categoriasAndSubcategorias;

  Future<void> getCategoriasAndSubcategorias() async {
    final categorias =
        await getCategoriasMetodos.getCategoriasComSubcategorias();
    _categoriasAndSubcategorias = categorias;
    notifyListeners();
  }

  String? _ordenarPor;
  String? get ordenarPor => _ordenarPor;

  Future<void> setOrdenarPor(String? value) async {
    _ordenarPor = value;
    await contarProdutosComFiltrosPersonalizados(
      isCategoryFilters: false,
    );
    notifyListeners();
  }

  bool? _freteGratis;
  bool? get freteGratis => _freteGratis;

  Future<void> toggleFreteGratis() async {
    if (_freteGratis == true) {
      _freteGratis = null;
    } else {
      _freteGratis = true;
    }
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  final TextEditingController precoMinController =
      TextEditingController();
  final TextEditingController precoMaxController =
      TextEditingController();

  void initPrecoControllers() {
    precoMinController.text =
        precoMinimo != null
            ? 'R\$ ${precoMinimo!.toStringAsFixed(2).replaceAll('.', ',')}'
            : '';
    precoMaxController.text =
        precoMaximo != null
            ? 'R\$ ${precoMaximo!.toStringAsFixed(2).replaceAll('.', ',')}'
            : '';
  }

  double? _precoMinimo;
  double? _precoMaximo;

  double? get precoMinimo => _precoMinimo;
  double? get precoMaximo => _precoMaximo;

  Future<void> setPrecoMinimo(
    double? value, {
    required bool isCategoryFilters,
    int? intCategoria,
  }) async {
    _precoMinimo = value;
    await contarProdutosComFiltrosPersonalizados(
      isCategoryFilters: isCategoryFilters,
      intCategoria: intCategoria,
    );
    notifyListeners();
  }

  Future<void> setPrecoMaximo(
    double? value, {
    required bool isCategoryFilters,
    int? intCategoria,
  }) async {
    _precoMaximo = value;
    await contarProdutosComFiltrosPersonalizados(
      isCategoryFilters: isCategoryFilters,
      intCategoria: intCategoria,
    );
    notifyListeners();
  }

  Set<String> _lojasSelecionadas = {};
  Set<String> get lojasSelecionadas => _lojasSelecionadas;

  void toggleLoja(
    String nomeLoja, {
    required bool isCategoryFilters,
    int? intCategoria,
  }) async {
    if (_lojasSelecionadas.contains(nomeLoja)) {
      _lojasSelecionadas.remove(nomeLoja);
    } else {
      _lojasSelecionadas.add(nomeLoja);
    }
    await contarProdutosComFiltrosPersonalizados(
      isCategoryFilters: isCategoryFilters,
      intCategoria: intCategoria,
    );
    notifyListeners();
  }

  final Set<String> _subcategoriasSelecionadas = {};
  Set<String> get subcategoriasSelecionadas =>
      _subcategoriasSelecionadas;

  Future<void> toggleSubcategoria(
    int idCategoria,
    int idSubcategoria,
  ) async {
    final key = '$idCategoria:$idSubcategoria';
    if (_subcategoriasSelecionadas.contains(key)) {
      _subcategoriasSelecionadas.remove(key);
    } else {
      _subcategoriasSelecionadas.add(key);
    }
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  String? precoValidator(String? value) {
    if (value == null || value.isEmpty) return null;

    final parsed = double.tryParse(value.replaceAll(',', '.'));
    if (parsed == null) return "Valor inválido";

    if (parsed < 0) return "Preço não pode ser negativo";

    return null;
  }

  void filtrosAtivosCount() {
    int count = 0;

    if (ordenarPor != null && ordenarPor!.isNotEmpty) count++;
    if (freteGratis == true) count++;
    if (precoMinimo != null || precoMaximo != null) count++;
    if (lojasSelecionadas.isNotEmpty) count++;
    if (subcategoriasSelecionadas.isNotEmpty ||
        categoriasSelecionadas.isNotEmpty) {
      count++;
    }

    _filtrosAtivos = count;
    notifyListeners();
  }

  int _filtrosAtivos = 0;
  int get filtrosAtivos => _filtrosAtivos;

  incremnetFiltros() {
    _filtrosAtivos++;
    notifyListeners();
  }

  Set<int> _categoriasComVerMais = {};
  Set<int> _categoriasExpandidas = {};

  bool isCategoriaExpandida(int idCategoria) =>
      _categoriasExpandidas.contains(idCategoria);

  bool isVerMaisAtivo(int idCategoria) =>
      _categoriasComVerMais.contains(idCategoria);

  Future<void> toggleCategoriaExpandida(int idCategoria) async {
    if (_categoriasExpandidas.contains(idCategoria)) {
      _categoriasExpandidas.remove(idCategoria);
      _categoriasComVerMais.remove(
        idCategoria,
      ); // reseta o ver mais ao fechar
    } else {
      _categoriasExpandidas.add(idCategoria);
    }
    notifyListeners();
  }

  Future<void> toggleVerMais(int idCategoria) async {
    if (_categoriasComVerMais.contains(idCategoria)) {
      _categoriasComVerMais.remove(idCategoria);
    } else {
      _categoriasComVerMais.add(idCategoria);
    }
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  Future<void> adicionarCategoriaSelecionada(int idCategoria) async {
    _categoriasSelecionadas.add(idCategoria);
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  Future<void> removerCategoriaSelecionada(int idCategoria) async {
    _categoriasSelecionadas.remove(idCategoria);
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  // TODO: ESSA FUNCAO TEM USO EXCLUSIVO PARA O FILTRO DE CATEGORIA
  Future<void> toggleAllSubcategoriasAPENASPARAFILTOS(
    int idCategoria,
  ) async {
    final categoria = _categoriasAndSubcategorias.firstWhere(
      (cat) => cat.id == idCategoria,
      orElse: () => throw Exception('Categoria não encontrada'),
    );

    final todasSelecionadas = categoria.subcategorias.every((sub) {
      final key = '$idCategoria:${sub.idSubcategoria}';
      return _subcategoriasSelecionadas.contains(key);
    });

    if (todasSelecionadas) {
      await deselecionarTodasSubcategoriasDaCategoria(idCategoria);
      // zera o contador de filtros
      _filtrosAtivos = 0;
      // zera o número de produtos obetidos
      _quantidadeResultadosFiltrados = 0;

      notifyListeners();
    } else {
      // antes disso, descelecionar todas subcategorias e categorias selecionadas
      _categoriasSelecionadas.clear();
      _subcategoriasSelecionadas.clear();
      await selecionarTodasSubcategoriasDaCategoria(idCategoria);
    }

    notifyListeners();
  }

  bool todasSubcategoriasSelecionadas(int idCategoria) {
    final categoria = _categoriasAndSubcategorias.firstWhere(
      (cat) => cat.id == idCategoria,
      orElse: () => throw Exception('Categoria não encontrada'),
    );

    return categoria.subcategorias.every((sub) {
      final key = '$idCategoria:${sub.idSubcategoria}';
      return _subcategoriasSelecionadas.contains(key);
    });
  }

  Future<void> selecionarTodasSubcategoriasDaCategoria(
    int idCategoria,
  ) async {
    final categoria = _categoriasAndSubcategorias.firstWhere(
      (cat) => cat.id == idCategoria,
    );
    for (final sub in categoria.subcategorias) {
      final key = '$idCategoria:${sub.idSubcategoria}';
      _subcategoriasSelecionadas.add(key);
    }
    _categoriasSelecionadas.add(idCategoria);
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  Future<void> deselecionarTodasSubcategoriasDaCategoria(
    int idCategoria,
  ) async {
    final categoria = _categoriasAndSubcategorias.firstWhere(
      (cat) => cat.id == idCategoria,
    );
    for (final sub in categoria.subcategorias) {
      final key = '$idCategoria:${sub.idSubcategoria}';
      _subcategoriasSelecionadas.remove(key);
    }
    // deselecionar categoria
    _categoriasSelecionadas.remove(idCategoria);
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  Future<void> toggleTodasSubcategoriasDaCategoria(
    int idCategoria,
  ) async {
    final categoria = _categoriasAndSubcategorias.firstWhere(
      (cat) => cat.id == idCategoria,
    );

    final todasSelecionadas = categoria.subcategorias.every((sub) {
      final key = '$idCategoria:${sub.idSubcategoria}';
      return _subcategoriasSelecionadas.contains(key);
    });

    if (todasSelecionadas) {
      await deselecionarTodasSubcategoriasDaCategoria(idCategoria);
    } else {
      await selecionarTodasSubcategoriasDaCategoria(idCategoria);
    }
  }

  Set<int> _categoriasSelecionadas = {};
  Set<int> get categoriasSelecionadas => _categoriasSelecionadas;

  Future<void> toggleCategoria(int idCategoria) async {
    if (_categoriasSelecionadas.contains(idCategoria)) {
      _categoriasSelecionadas.remove(idCategoria);
    } else {
      _categoriasSelecionadas.add(idCategoria);
    }
    await contarProdutosComFiltrosPersonalizados();
    notifyListeners();
  }

  CategoriaMenu? _categoriaMenu;

  CategoriaMenu? get categoriaMenu => _categoriaMenu;
  Future<void> setCategoriaMenu(CategoriaMenu categoriaMenu) async {
    _categoriaMenu = categoriaMenu;
    notifyListeners();
  }

  setCleearCategoriaMenu() {
    _categoriaMenu = null;
    notifyListeners();
  }

  Future<void> limparTodosOsFiltros({
    required bool isCategoryFilters,
  }) async {
    _ordenarPor = null;
    _freteGratis = false;
    _precoMinimo = null;
    _precoMaximo = null;
    _lojasSelecionadas.clear();
    _subcategoriasSelecionadas.clear();
    _categoriasSelecionadas.clear();
    _categoriasComVerMais.clear();
    _categoriasExpandidas.clear();
    _produtosFiltradosByUser.clear();
    _paginaAtualFiltro = 0;
    _quantidadeResultadosFiltrados = null;
    _loadingContagemResultados = false;

    // LIMPA CAMPOS VISUAIS
    precoMinController.clear();
    precoMaxController.clear();

    filtrosAtivosCount();

    // Reset filtro rápido
    if (isCategoryFilters) {
      setFilterCategory("Recentes");
      filterProdutosPorCategoria(_categoriaMenu?.nome ?? '');
      getTotalPorCategoria(_categoriaMenu?.nome ?? '');
    } else {
      setFilter("Recentes");
      await refreshProdutos();
      setTipoLista(TipoListaProdutos.geral);
    }

    notifyListeners();
  }

  int _paginaAtualFiltro = 0;
  final int _limitePorPagina = 20;
  bool _carregandoMaisResultadosFiltro = false;

  Future<void> buscarProdutosComFiltrosPersonalizados({
    bool reset = false,
    required bool isCategoryFilters,
    int? intCategoria,
  }) async {
    _carregandoMaisResultadosFiltro = true;
    notifyListeners();

    if (reset) {
      _paginaAtualFiltro = 0;
      _produtosFiltradosByUser.clear();
    }

    final categoriasExtraidas =
        subcategoriasSelecionadas
            .map((e) => int.tryParse(e.split(':')[0]))
            .whereType<int>()
            .toSet()
            .toList();

    final categoriasSelecionadasFinal =
        _categoriasSelecionadas.isNotEmpty
            ? _categoriasSelecionadas.toList()
            : categoriasExtraidas;

    final produtos = await _getProdutos.buscarProdutosFiltrados(
      categoriasSelecionadas:
          intCategoria != null && isCategoryFilters == true
              ? [intCategoria]
              : categoriasSelecionadasFinal.isNotEmpty
              ? categoriasSelecionadasFinal
              : null,
      ordenarPor: ordenarPor,
      freteGratis: freteGratis,
      precoMinimo: precoMinimo,
      precoMaximo: precoMaximo,
      lojasSelecionadas:
          lojasSelecionadas.isNotEmpty
              ? lojasSelecionadas.toList()
              : null,
      subcategoriasSelecionadas:
          subcategoriasSelecionadas.isNotEmpty
              ? subcategoriasSelecionadas
                  .map((e) => int.tryParse(e.split(':')[1]))
                  .whereType<int>()
                  .toList()
              : null,
      limit: _limitePorPagina,
      offset: _paginaAtualFiltro * _limitePorPagina,
    );

    // Sempre ordena inicialmente por ID (Recentes)
    produtos.sort((a, b) => b.id.compareTo(a.id));

    if (_paginaAtualFiltro == 0) {
      _produtosFiltradosByUser = produtos;
      setTipoLista(TipoListaProdutos.filtrosPersonalizados);

      // Aplica filtro rápido ativo se existir
      if (selectedFilter.isNotEmpty) {
        _produtosFiltradosByUser = aplicarOrdenacaoRapida(
          _produtosFiltradosByUser,
          selectedFilter,
        );
      }
    } else {
      // Adiciona sem duplicatas
      final idsExistentes =
          _produtosFiltradosByUser.map((p) => p.id).toSet();
      final novosProdutos = produtos.where(
        (p) => !idsExistentes.contains(p.id),
      );
      _produtosFiltradosByUser.addAll(novosProdutos);

      // Reaplica ordenação no conjunto total se houver filtro rápido
      if (selectedFilter.isNotEmpty) {
        _produtosFiltradosByUser = aplicarOrdenacaoRapida(
          _produtosFiltradosByUser,
          selectedFilter,
        );
      }
    }

    _paginaAtualFiltro++;
    _carregandoMaisResultadosFiltro = false;
    notifyListeners();
  }

  bool get carregandoMaisResultadosFiltro =>
      _carregandoMaisResultadosFiltro;

  int? _quantidadeResultadosFiltrados;

  bool _loadingContagemResultados = false;

  int? get quantidadeResultadosFiltrados =>
      _quantidadeResultadosFiltrados;
  bool get loadingContagemResultados => _loadingContagemResultados;

  Future<void> contarProdutosComFiltrosPersonalizados({
    bool? isCategoryFilters,
    int? intCategoria,
  }) async {
    filtrosAtivosCount();
    _loadingContagemResultados = true;
    notifyListeners();

    final categoriasExtraidas =
        subcategoriasSelecionadas
            .map((e) => int.tryParse(e.split(':')[0]))
            .whereType<int>()
            .toSet()
            .toList();

    final categoriasSelecionadasFinal =
        _categoriasSelecionadas.isNotEmpty
            ? _categoriasSelecionadas.toList()
            : categoriasExtraidas;

    _quantidadeResultadosFiltrados = await _getProdutos
        .contarProdutosFiltrados(
          categoriasSelecionadas:
              intCategoria != null && isCategoryFilters == true
                  ? [intCategoria]
                  : categoriasSelecionadasFinal.isNotEmpty
                  ? categoriasSelecionadasFinal
                  : null,

          freteGratis: freteGratis,
          precoMinimo: precoMinimo,
          precoMaximo: precoMaximo,
          lojasSelecionadas:
              lojasSelecionadas.isNotEmpty
                  ? lojasSelecionadas.toList()
                  : null,
          subcategoriasSelecionadas:
              subcategoriasSelecionadas.isNotEmpty
                  ? subcategoriasSelecionadas
                      .map((e) => int.tryParse(e.split(':')[1]))
                      .whereType<int>()
                      .toList()
                  : null,
        );

    _loadingContagemResultados = false;
    notifyListeners();
  }

  List<Product> _produtosFiltradosByUser = [];
  List<Product> get produtosFiltradosByUser =>
      _produtosFiltradosByUser;

  bool get isFiltroPersonalizadoAtivo =>
      _produtosFiltradosByUser.isNotEmpty;

  void setProdutosFiltradosByUser(List<Product> produtos) {
    _produtosFiltradosByUser = produtos;
    notifyListeners();
  }

  void limparProdutosFiltradosByUser() {
    _produtosFiltradosByUser = [];
    // Usar addPostFrameCallback para evitar notifyListeners durante build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
}
